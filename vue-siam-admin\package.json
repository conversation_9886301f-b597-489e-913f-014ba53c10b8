{"name": "vue-siam-admin", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "webpack-dev-server --mode=development", "build": "webpack --mode=production --progress --hide-modules"}, "dependencies": {"acorn": "^6.1.1", "axios": "^0.18.0", "echarts": "^4.9.0", "element-ui": "^2.8.2", "jquery": "^3.4.1", "json-bigint": "^1.0.0", "quill": "^1.3.6", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module": "^3.0.0", "v-charts": "^1.19.0", "vue": "^2.6.12", "vue-cropper": "^0.5.6", "vue-echarts": "^5.0.0-beta.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.4", "vuex": "^3.1.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "axios-mock-adapter": "^1.7.1", "babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.0.0", "babel-preset-stage-3": "^6.24.1", "babel-register": "^6.22.0", "clean-webpack-plugin": "^2.0.1", "copy-webpack-plugin": "^5.1.2", "cross-env": "^5.2.0", "css-loader": "^0.28.8", "define-properties": "^1.1.3", "file-loader": "^1.1.6", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "json-loader": "^0.5.4", "mini-css-extract-plugin": "^0.6.0", "node-sass": "^4.14.1", "sass-loader": "^6.0.6", "style-loader": "^0.23.1", "url-loader": "^1.1.2", "vue-loader": "^15.9.6", "vue-style-loader": "^3.0.3", "vue-template-compiler": "^2.6.12", "webpack": "^4.36.1", "webpack-cli": "^3.3.6", "webpack-dev-server": "^3.4.1", "webpack-merge": "^4.2.1"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}