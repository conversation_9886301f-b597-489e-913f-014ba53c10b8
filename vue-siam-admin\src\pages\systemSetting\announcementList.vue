<template>
	<section>
		<!--工具条-->
		<el-col :span="24" class="toolbar" style="padding-bottom: 0px;">
			<el-form :inline="true" :model="searchMsg">
				<el-form-item label="公告标题" prop="title">
					<el-input v-model="searchMsg.title" clearable placeholder="公告标题"></el-input>
				</el-form-item>
				<el-form-item label="状态" prop="status">
					<el-select v-model="searchMsg.status" clearable placeholder="请选择状态">
						<el-option label="草稿" :value="0"></el-option>
						<el-option label="已发布" :value="1"></el-option>
						<el-option label="已撤回" :value="2"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="发布人" prop="publishName">
					<el-input v-model="searchMsg.publishName" clearable placeholder="发布人"></el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="getList(1)">查询</el-button>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="gotoAdd()">新增公告</el-button>
				</el-form-item>
			</el-form>
		</el-col>
		<!--列表-->
		<el-table :data="list" highlight-current-row v-loading="listLoading" style="width: 100%;" :cell-style="cellStyle" :header-cell-style="headerCellStyle">
			<el-table-column type="index" label="序号" width="60">
				<template slot-scope="scope">
					<span>{{(searchMsg.pageNo - 1) * searchMsg.pageSize + scope.$index + 1}}</span>
				</template>		
			</el-table-column>
			<el-table-column prop="title" label="公告标题" min-width="200" show-overflow-tooltip></el-table-column>
			<el-table-column prop="status" label="状态" width="100">
				<template slot-scope="scope">
					<el-tag :type="getStatusType(scope.row.status)">{{ formatStatus(scope.row) }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column prop="publishName" label="发布人" width="120"></el-table-column>
			<el-table-column prop="publishTime" label="发布时间" width="160" :formatter="formatTime"></el-table-column>
			<el-table-column prop="startTime" label="生效时间" width="160" :formatter="formatTime"></el-table-column>
			<el-table-column prop="endTime" label="失效时间" width="160" :formatter="formatTime"></el-table-column>
			<el-table-column prop="createdTime" label="创建时间" width="160" :formatter="formatTime"></el-table-column>
			<el-table-column label="操作" fixed="right" width="200">
				<template slot-scope="scope">
					<el-button size="small" @click="gotoEdit(scope.row)">编辑</el-button>
					<el-button v-if="scope.row.status === 0" size="small" type="success" @click="publishAnnouncement(scope.row.id)">发布</el-button>
					<el-button v-if="scope.row.status === 1" size="small" type="warning" @click="withdrawAnnouncement(scope.row.id)">撤回</el-button>
					<el-button type="danger" size="small" @click="handleDel(scope.row.id)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!--分页-->
		<el-col :span="24" class="toolbar">
			<el-pagination
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:page-sizes="[10, 20, 50, 100]"
				:page-size="searchMsg.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				style="float:right;">
			</el-pagination>
		</el-col>
	</section>
</template>

<script>
	export default {
		data() {
			return {
				searchMsg: {
					pageNo: 1,
					pageSize: 20,
					title: '',
					status: '',
					publishName: '',
					orderBy: 'created_time',
					orderType: 'desc'
				},
				list: [],
				total: 0,
				listLoading: false
			}
		},
		methods: {
			cellStyle({row, column, rowIndex, columnIndex}){
				return "text-align:center";
			},
			headerCellStyle({row, rowIndex}){
				return "text-align:center";
			},
			formatTime(row, column) {
				let date = new Date(row[column.property]);
				let str = this.$utils.formatDate(date, 'yyyy-MM-dd hh:mm:ss');
				// 处理数据库中时间为NULL场景
				if(row[column.property]==undefined || str=="1970-01-01 08:00:00"){
					str = "-";
				}
				return str;
			},
			formatStatus(row, column) {
				const statusMap = {
					0: '草稿',
					1: '已发布',
					2: '已撤回'
				};
				return statusMap[row.status] || '未知';
			},
			getStatusType(status) {
				const typeMap = {
					0: '',
					1: 'success',
					2: 'warning'
				};
				return typeMap[status] || '';
			},
			handleSizeChange(val) {
				this.searchMsg.pageSize = val;
				this.getList();
			},			
			handleCurrentChange(val) {
				this.searchMsg.pageNo = val;
				this.getList();
			},
			getList(pageNoParam) { // 获取公告列表
				if(pageNoParam){
					this.searchMsg.pageNo = pageNoParam;
				}
				let vue = this
				let param = Object.assign({}, vue.searchMsg);
				vue.listLoading = true;
				vue.$http.post(vue, '/api-merchant/rest/admin/announcements/selectByExample', param,
					(vue, data) => {
						vue.list = data.data.records || data.data.list || []
						vue.total = data.data.total || 0
						vue.listLoading = false;
					},(error, data)=> {
						vue.listLoading = false;
						vue.$message({
							showClose: true,
							message: '获取公告列表失败',
							type: 'error'
						});
					}
				);
			},
			gotoAdd() {
				this.$router.push('/addAnnouncement');
			},
			gotoEdit(row) {
				this.$router.push({
					path: '/editAnnouncement',
					query: { id: row.id }
				});
			},
			publishAnnouncement(id) {
				this.$confirm('确认发布此公告吗？', '提示', {
					type: 'warning'
				}).then(() => {
					let vue = this;
					vue.$http.post(vue, '/api-merchant/rest/admin/announcements/publish', { id: id },
						(vue, data) => {
							vue.$message({
								showClose: true,
								message: '发布成功',
								type: 'success'
							});
							vue.getList();
						},(error, data)=> {
							vue.$message({
								showClose: true,
								message: '发布失败',
								type: 'error'
							});
						}
					);
				});
			},
			withdrawAnnouncement(id) {
				this.$confirm('确认撤回此公告吗？', '提示', {
					type: 'warning'
				}).then(() => {
					let vue = this;
					vue.$http.post(vue, '/api-merchant/rest/admin/announcements/withdraw', { id: id },
						(vue, data) => {
							vue.$message({
								showClose: true,
								message: '撤回成功',
								type: 'success'
							});
							vue.getList();
						},(error, data)=> {
							vue.$message({
								showClose: true,
								message: '撤回失败',
								type: 'error'
							});
						}
					);
				});
			},
			handleDel(id) {
				this.$confirm('确认删除此公告吗？删除后无法恢复！', '提示', {
					type: 'warning'
				}).then(() => {
					let vue = this;
					vue.$http.delete(vue, '/api-merchant/rest/admin/announcements/delete?id=' + id, {},
						(vue, data) => {
							vue.$message({
								showClose: true,
								message: '删除成功',
								type: 'success'
							});
							vue.getList();
						},(error, data)=> {
							vue.$message({
								showClose: true,
								message: '删除失败',
								type: 'error'
							});
						}
					);
				});
			}
		},
		mounted() {
			this.getList();
		}
	}
</script>

<style scoped>
	.toolbar {
		background: #f2f6fc;
		padding: 10px;
		margin: 10px 0px;
	}
	.toolbar .el-form-item {
		margin-bottom: 10px;
	}
</style>
