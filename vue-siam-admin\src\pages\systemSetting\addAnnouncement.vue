<template>
	<section>
		<el-card class="box-card">
			<div slot="header" class="clearfix">
				<span>新增公告</span>
				<el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
			</div>
			
			<el-form :model="form" :rules="rules" ref="form" label-width="120px" style="max-width: 800px;">
				<el-form-item label="公告标题" prop="title">
					<el-input v-model="form.title" placeholder="请输入公告标题" maxlength="200" show-word-limit></el-input>
				</el-form-item>
				
				<el-form-item label="公告内容" prop="content">
					<div style="border: 1px solid #ccc;">
						<quill-editor
							v-model="form.content"
							ref="myQuillEditor"
							:options="editorOption"
							style="min-height: 300px;">
						</quill-editor>
					</div>
				</el-form-item>
				
				<el-form-item label="生效时间" prop="startTime">
					<el-date-picker
						v-model="form.startTime"
						type="datetime"
						placeholder="选择生效时间"
						format="yyyy-MM-dd HH:mm:ss"
						value-format="yyyy-MM-dd HH:mm:ss"
						style="width: 100%;">
					</el-date-picker>
					<div class="form-tip">不选择表示立即生效</div>
				</el-form-item>
				
				<el-form-item label="失效时间" prop="endTime">
					<el-date-picker
						v-model="form.endTime"
						type="datetime"
						placeholder="选择失效时间"
						format="yyyy-MM-dd HH:mm:ss"
						value-format="yyyy-MM-dd HH:mm:ss"
						style="width: 100%;">
					</el-date-picker>
					<div class="form-tip">不选择表示永不过期</div>
				</el-form-item>
				
				<el-form-item>
					<el-button type="primary" @click="saveDraft" :loading="saveLoading">保存草稿</el-button>
					<el-button type="success" @click="saveAndPublish" :loading="publishLoading">保存并发布</el-button>
					<el-button @click="goBack">取消</el-button>
				</el-form-item>
			</el-form>
		</el-card>
	</section>
</template>

<script>
	import { quillEditor } from 'vue-quill-editor'
	import 'quill/dist/quill.core.css'
	import 'quill/dist/quill.snow.css'
	import 'quill/dist/quill.bubble.css'

	export default {
		components: {
			quillEditor
		},
		data() {
			return {
				form: {
					title: '',
					content: '',
					startTime: '',
					endTime: '',
					status: 0 // 默认草稿状态
				},
				rules: {
					title: [
						{ required: true, message: '请输入公告标题', trigger: 'blur' },
						{ min: 1, max: 200, message: '标题长度在 1 到 200 个字符', trigger: 'blur' }
					],
					content: [
						{ required: true, message: '请输入公告内容', trigger: 'blur' }
					]
				},
				editorOption: {
					theme: 'snow',
					placeholder: '请输入公告内容...',
					modules: {
						toolbar: [
							['bold', 'italic', 'underline', 'strike'],
							['blockquote', 'code-block'],
							[{ 'header': 1 }, { 'header': 2 }],
							[{ 'list': 'ordered'}, { 'list': 'bullet' }],
							[{ 'script': 'sub'}, { 'script': 'super' }],
							[{ 'indent': '-1'}, { 'indent': '+1' }],
							[{ 'direction': 'rtl' }],
							[{ 'size': ['small', false, 'large', 'huge'] }],
							[{ 'header': [1, 2, 3, 4, 5, 6, false] }],
							[{ 'color': [] }, { 'background': [] }],
							[{ 'font': [] }],
							[{ 'align': [] }],
							['clean'],
							['link', 'image']
						]
					}
				},
				saveLoading: false,
				publishLoading: false
			}
		},
		methods: {
			goBack() {
				this.$router.push('/announcementList');
			},
			validateForm() {
				return new Promise((resolve, reject) => {
					this.$refs.form.validate((valid) => {
						if (valid) {
							resolve();
						} else {
							reject();
						}
					});
				});
			},
			saveDraft() {
				this.validateForm().then(() => {
					this.saveLoading = true;
					this.form.status = 0; // 草稿状态
					this.saveAnnouncement();
				}).catch(() => {
					this.$message.error('请完善表单信息');
				});
			},
			saveAndPublish() {
				this.validateForm().then(() => {
					this.$confirm('确认保存并发布此公告吗？', '提示', {
						type: 'warning'
					}).then(() => {
						this.publishLoading = true;
						this.form.status = 0; // 先保存为草稿
						this.saveAnnouncement(true);
					});
				}).catch(() => {
					this.$message.error('请完善表单信息');
				});
			},
			saveAnnouncement(isPublish = false) {
				let vue = this;
				let param = Object.assign({}, vue.form);

				// 时间格式处理
				if (param.startTime) {
					param.startTime = param.startTime.replace('T', ' ');
				}
				if (param.endTime) {
					param.endTime = param.endTime.replace('T', ' ');
				}

				vue.$http.post(vue, '/api-merchant/rest/admin/announcements/insert', param,
					(vue, data) => {
						if (isPublish && data.data && data.data.id) {
							// 保存成功后立即发布
							vue.publishAnnouncement(data.data.id);
						} else {
							vue.saveLoading = false;
							vue.publishLoading = false;
							vue.$message({
								showClose: true,
								message: '草稿保存成功',
								type: 'success'
							});
							vue.goBack();
						}
					},(error, data)=> {
						vue.saveLoading = false;
						vue.publishLoading = false;
						vue.$message({
							showClose: true,
							message: isPublish ? '公告发布失败' : '草稿保存失败',
							type: 'error'
						});
					}
				);
			},
			publishAnnouncement(id) {
				let vue = this;
				vue.$http.post(vue, '/api-merchant/rest/admin/announcements/publish', { id: id },
					(vue, data) => {
						vue.publishLoading = false;
						vue.$message({
							showClose: true,
							message: '公告发布成功',
							type: 'success'
						});
						vue.goBack();
					},(error, data)=> {
						vue.publishLoading = false;
						vue.$message({
							showClose: true,
							message: '公告发布失败',
							type: 'error'
						});
					}
				);
			}
		}
	}
</script>

<style scoped>
	.box-card {
		margin: 20px;
	}
	.form-tip {
		font-size: 12px;
		color: #999;
		margin-top: 5px;
	}
	.clearfix:before,
	.clearfix:after {
		display: table;
		content: "";
	}
	.clearfix:after {
		clear: both
	}
</style>

<style>
	.ql-editor {
		min-height: 300px;
	}
</style>
