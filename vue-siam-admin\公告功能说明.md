# 公告功能说明

## 功能概述
公告功能提供了完整的公告管理能力，包括公告的创建、编辑、发布、撤回、查询等操作。

## 数据库表结构
表名：`sys_announcements`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键ID |
| title | VARCHAR(200) | 公告标题 |
| content | TEXT | 公告内容 |
| status | TINYINT | 状态(0:草稿,1:已发布,2:已撤回) |
| publish_time | DATETIME | 发布时间 |
| publish_name | VARCHAR(200) | 发布人 |
| start_time | DATETIME | 生效时间开始时间 |
| end_time | DATETIME | 失效时间结束时间 |
| created_name | VARCHAR(200) | 创建人 |
| created_time | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |
| updated_name | VARCHAR(200) | 修改人 |

## API接口说明

### 1. 保存公告
- **接口地址**: `POST /api-merchant/rest/admin/announcements/insert`
- **请求参数**: `SysAnnouncementsParam`
- **功能**: 创建新的公告（默认为草稿状态）

### 2. 修改公告
- **接口地址**: `PUT /api-merchant/rest/admin/announcements/update`
- **请求参数**: `SysAnnouncementsParam`
- **功能**: 修改现有公告信息

### 3. 删除公告
- **接口地址**: `DELETE /api-merchant/rest/admin/announcements/delete`
- **请求参数**: `id` (公告ID)
- **功能**: 删除指定公告

### 4. 根据ID查询公告
- **接口地址**: `GET /api-merchant/rest/admin/announcements/selectById`
- **请求参数**: `id` (公告ID)
- **功能**: 获取指定公告的详细信息

### 5. 分页查询公告
- **接口地址**: `POST /api-merchant/rest/admin/announcements/selectByExample`
- **请求参数**: `SysAnnouncementsExample`
- **功能**: 根据条件分页查询公告列表

### 6. 发布公告
- **接口地址**: `POST /api-merchant/rest/admin/announcements/publish`
- **请求参数**: `id` (公告ID)
- **功能**: 将草稿状态的公告发布（发布人自动获取当前登录用户）

### 7. 撤回公告
- **接口地址**: `POST /api-merchant/rest/admin/announcements/withdraw`
- **请求参数**: `id` (公告ID)
- **功能**: 撤回已发布的公告（撤回人自动获取当前登录用户）

### 8. 查询所有已发布公告
- **接口地址**: `GET /api-merchant/rest/admin/announcements/selectAllPublished`
- **功能**: 获取所有已发布且在有效期内的公告（自动过滤过期的公告）

## 状态说明
- **0**: 草稿 - 公告已创建但未发布
- **1**: 已发布 - 公告已发布，用户可见
- **2**: 已撤回 - 公告已撤回，用户不可见

## 时间有效性说明
- **生效时间（startTime）**: 公告开始生效的时间，为空表示立即生效
- **失效时间（endTime）**: 公告失效的时间，为空表示永不过期
- **查询逻辑**: 
  - 分页查询接口：不受时间限制，可查询所有状态的公告
  - 查询所有已发布公告接口：自动过滤已过期的公告（当前时间 > endTime）

## 使用示例

### 创建公告
```json
POST /api-merchant/rest/admin/announcements/insert
{
    "title": "系统维护通知",
    "content": "系统将于今晚22:00-24:00进行维护升级",
    "status": 0,
    "startTime": "2024/01/01 22:00:00",
    "endTime": "2024/01/01 24:00:00"
}
```

### 分页查询
```json
POST /api-merchant/rest/admin/announcements/selectByExample
{
    "pageNo": 1,
    "pageSize": 10,
    "title": "维护",
    "status": 1,
    "orderBy": "created_time",
    "orderType": "desc"
}
```

### 发布公告
```
POST /api-merchant/rest/admin/announcements/publish?id=1
```

## 注意事项
1. 只有草稿状态的公告才能发布
2. 只有已发布状态的公告才能撤回
3. "查询所有已发布公告"接口会自动过滤过期的公告，分页查询接口不受影响
4. 所有时间字段都使用GMT+8时区，前端传递时间格式为ISO 8601格式（如：2024-01-01T22:00:00）
5. 删除操作会直接删除数据库记录，请谨慎操作
6. 创建人、修改人、发布人信息会自动从当前登录用户获取，无需前端传递
