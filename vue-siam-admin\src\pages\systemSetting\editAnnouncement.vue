<template>
	<section>
		<el-card class="box-card" v-loading="pageLoading">
			<div slot="header" class="clearfix">
				<span>编辑公告</span>
				<el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
			</div>
			
			<el-form :model="form" :rules="rules" ref="form" label-width="120px" style="max-width: 800px;">
				<el-form-item label="公告标题" prop="title">
					<el-input v-model="form.title" placeholder="请输入公告标题" maxlength="200" show-word-limit></el-input>
				</el-form-item>
				
				<el-form-item label="公告内容" prop="content">
					<div style="border: 1px solid #ccc;">
						<quill-editor
							v-model="form.content"
							ref="myQuillEditor"
							:options="editorOption"
							style="min-height: 300px;">
						</quill-editor>
					</div>
				</el-form-item>
				
				<el-form-item label="生效时间" prop="startTime">
					<el-date-picker
						v-model="form.startTime"
						type="datetime"
						placeholder="选择生效时间"
						format="yyyy-MM-dd HH:mm:ss"
						value-format="yyyy-MM-dd HH:mm:ss"
						style="width: 100%;">
					</el-date-picker>
					<div class="form-tip">不选择表示立即生效</div>
				</el-form-item>
				
				<el-form-item label="失效时间" prop="endTime">
					<el-date-picker
						v-model="form.endTime"
						type="datetime"
						placeholder="选择失效时间"
						format="yyyy-MM-dd HH:mm:ss"
						value-format="yyyy-MM-dd HH:mm:ss"
						style="width: 100%;">
					</el-date-picker>
					<div class="form-tip">不选择表示永不过期</div>
				</el-form-item>
				
				<el-form-item label="当前状态">
					<el-tag :type="getStatusType(form.status)">{{ getStatusText(form.status) }}</el-tag>
				</el-form-item>
				
				<el-form-item>
					<el-button type="primary" @click="saveAnnouncement" :loading="saveLoading">保存修改</el-button>
					<el-button v-if="form.status === 0" type="success" @click="publishAnnouncement" :loading="publishLoading">发布</el-button>
					<el-button v-if="form.status === 1" type="warning" @click="withdrawAnnouncement" :loading="withdrawLoading">撤回</el-button>
					<el-button @click="goBack">取消</el-button>
				</el-form-item>
			</el-form>
		</el-card>
	</section>
</template>

<script>
	import { quillEditor } from 'vue-quill-editor'
	import 'quill/dist/quill.core.css'
	import 'quill/dist/quill.snow.css'
	import 'quill/dist/quill.bubble.css'

	export default {
		components: {
			quillEditor
		},
		data() {
			return {
				form: {
					id: '',
					title: '',
					content: '',
					startTime: '',
					endTime: '',
					status: 0
				},
				rules: {
					title: [
						{ required: true, message: '请输入公告标题', trigger: 'blur' },
						{ min: 1, max: 200, message: '标题长度在 1 到 200 个字符', trigger: 'blur' }
					],
					content: [
						{ required: true, message: '请输入公告内容', trigger: 'blur' }
					]
				},
				editorOption: {
					theme: 'snow',
					placeholder: '请输入公告内容...',
					modules: {
						toolbar: [
							['bold', 'italic', 'underline', 'strike'],
							['blockquote', 'code-block'],
							[{ 'header': 1 }, { 'header': 2 }],
							[{ 'list': 'ordered'}, { 'list': 'bullet' }],
							[{ 'script': 'sub'}, { 'script': 'super' }],
							[{ 'indent': '-1'}, { 'indent': '+1' }],
							[{ 'direction': 'rtl' }],
							[{ 'size': ['small', false, 'large', 'huge'] }],
							[{ 'header': [1, 2, 3, 4, 5, 6, false] }],
							[{ 'color': [] }, { 'background': [] }],
							[{ 'font': [] }],
							[{ 'align': [] }],
							['clean'],
							['link', 'image']
						]
					}
				},
				pageLoading: false,
				saveLoading: false,
				publishLoading: false,
				withdrawLoading: false
			}
		},
		methods: {
			goBack() {
				this.$router.push('/announcementList');
			},
			getStatusType(status) {
				const typeMap = {
					0: '',
					1: 'success',
					2: 'warning'
				};
				return typeMap[status] || '';
			},
			getStatusText(status) {
				const textMap = {
					0: '草稿',
					1: '已发布',
					2: '已撤回'
				};
				return textMap[status] || '未知';
			},
			loadAnnouncementDetail() {
				const id = this.$route.query.id;
				if (!id) {
					this.$message.error('缺少公告ID参数');
					this.goBack();
					return;
				}

				this.pageLoading = true;
				let vue = this;
				vue.$http.get(vue, '/api-merchant/rest/admin/announcements/selectById?id=' + id,
					(vue, data) => {
						vue.pageLoading = false;
						if (data.data) {
							vue.form = Object.assign({}, data.data);
							// 时间格式处理
							if (vue.form.startTime) {
								vue.form.startTime = vue.form.startTime.replace(' ', 'T');
							}
							if (vue.form.endTime) {
								vue.form.endTime = vue.form.endTime.replace(' ', 'T');
							}
						}
					},(error, data)=> {
						vue.pageLoading = false;
						vue.$message({
							showClose: true,
							message: '获取公告详情失败',
							type: 'error'
						});
						vue.goBack();
					}
				);
			},
			validateForm() {
				return new Promise((resolve, reject) => {
					this.$refs.form.validate((valid) => {
						if (valid) {
							resolve();
						} else {
							reject();
						}
					});
				});
			},
			saveAnnouncement() {
				this.validateForm().then(() => {
					this.saveLoading = true;
					let vue = this;
					let param = Object.assign({}, vue.form);
					
					// 时间格式处理
					if (param.startTime) {
						param.startTime = param.startTime.replace('T', ' ');
					}
					if (param.endTime) {
						param.endTime = param.endTime.replace('T', ' ');
					}
					
					vue.$http.put(vue, '/api-merchant/rest/admin/announcements/update', param,
						(vue, data) => {
							vue.saveLoading = false;
							vue.$message({
								showClose: true,
								message: '保存成功',
								type: 'success'
							});
						},(error, data)=> {
							vue.saveLoading = false;
							vue.$message({
								showClose: true,
								message: '保存失败',
								type: 'error'
							});
						}
					);
				}).catch(() => {
					this.$message.error('请完善表单信息');
				});
			},
			publishAnnouncement() {
				this.$confirm('确认发布此公告吗？', '提示', {
					type: 'warning'
				}).then(() => {
					this.publishLoading = true;
					let vue = this;
					vue.$http.post(vue, '/api-merchant/rest/admin/announcements/publish', { id: vue.form.id },
						(vue, data) => {
							vue.publishLoading = false;
							vue.$message({
								showClose: true,
								message: '发布成功',
								type: 'success'
							});
							vue.form.status = 1;
						},(error, data)=> {
							vue.publishLoading = false;
							vue.$message({
								showClose: true,
								message: '发布失败',
								type: 'error'
							});
						}
					);
				});
			},
			withdrawAnnouncement() {
				this.$confirm('确认撤回此公告吗？', '提示', {
					type: 'warning'
				}).then(() => {
					this.withdrawLoading = true;
					let vue = this;
					vue.$http.post(vue, '/api-merchant/rest/admin/announcements/withdraw', { id: vue.form.id },
						(vue, data) => {
							vue.withdrawLoading = false;
							vue.$message({
								showClose: true,
								message: '撤回成功',
								type: 'success'
							});
							vue.form.status = 2;
						},(error, data)=> {
							vue.withdrawLoading = false;
							vue.$message({
								showClose: true,
								message: '撤回失败',
								type: 'error'
							});
						}
					);
				});
			}
		},
		mounted() {
			this.loadAnnouncementDetail();
		}
	}
</script>

<style scoped>
	.box-card {
		margin: 20px;
	}
	.form-tip {
		font-size: 12px;
		color: #999;
		margin-top: 5px;
	}
	.clearfix:before,
	.clearfix:after {
		display: table;
		content: "";
	}
	.clearfix:after {
		clear: both
	}
</style>

<style>
	.ql-editor {
		min-height: 300px;
	}
</style>
